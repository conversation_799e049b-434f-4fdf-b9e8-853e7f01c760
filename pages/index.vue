<template>
  <div class="min-h-screen">
    <!-- Desktop Header -->
    <header v-if="!isMobile" class="p-8">
      <h1 class="font-title text-size-82 font-600 text-center line-height-none uppercase">Verbatims</h1>
      <span class="-mt-12 text-center font-sans font-400 block text-gray-600 dark:text-gray-400">
        Discover <b>{{ stats.quotes || 0 }}</b> quotes from films, tv series, video games, books, music, podcasts, documentaries.
        It's an open source community platform. You can post your own interesting quotes.
      </span>
    </header>

    <div v-else>
      <MobileHeroSection
        :quote="feed.quotes?.value?.[0]"
        @quote-interaction="openAddQuoteDrawer"
      />

      <MobileRecentAuthors
        @add-author="handleAddAuthor"
        @show-more="navigateTo('/authors')"
      />

      <MobileRecentReferences
        @add-reference="handleAddReference"
        @show-more="navigateTo('/references')"
      />

      <MobileProgressSection
        @show-details="navigateTo('/dashboard')"
        @main-action="navigateTo('/')"
        @secondary-action="navigateTo('/dashboard/stats')"
        @tertiary-action="navigateTo('/dashboard/settings')"
      />
    </div>

    <!-- Initial-only loading: show until first successful data load -->
    <div v-if="!isLanguageReady || feed.initialLoading?.value" class="flex items-center justify-center py-16">
      <div class="flex items-center gap-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
        <span class="text-gray-600 dark:text-gray-400">
          {{ !isLanguageReady ? 'Initializing...' : 'Loading quotes...' }}
        </span>
      </div>
    </div>

    <HomeEmptyView
      v-else-if="(stats.quotes === 0 || needsOnboarding) && !feed.quotesLoading?.value"
      :needs-onboarding="needsOnboarding"
      :onboarding-status="onboardingStatus"
      :stats="stats"
    />

    <!-- Desktop: Quotes Grid with Search -->
    <div v-else-if="!isMobile" class="mt-6 px-8 pb-16">
      <div class="flex gap-4 font-body mb-8">
        <div class="flex-grow-2 font-600">
          <UInput
            :model-value="feed.searchQuery?.value"
            @update:model-value="val => (feed.searchQuery.value = val)"
            placeholder="Search quotes..."
            leading="i-ph-magnifying-glass"
            size="md"
            :loading="feed.quotesLoading?.value"
          />
        </div>
        <div class="flex-1">
          <LanguageSelector @language-changed="feed.onLanguageChange" />
        </div>

        <div class="flex gap-4 items-center">
          <USelect
            :model-value="feed.selectedSortBy?.value"
            @update:model-value="val => (feed.selectedSortBy.value = val)"
            :items="feed.sortByOptions"
            placeholder="Sort by"
            size="sm"
            item-key="label"
            value-key="label"
          />
          <!-- Order Toggle: OFF = Desc (↓), ON = Asc (↑) -->
          <div class="flex items-center gap-2">
            <UToggle
              :model-value="feed.isAsc?.value"
              @update:model-value="val => (feed.isAsc.value = val)"
              size="sm"
              :label="feed.isAsc?.value ? 'i-ph-sort-descending-duotone' : 'i-ph-sort-ascending-duotone'"
              :aria-label="feed.isAsc?.value ? 'Ascending' : 'Descending'"
            />
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0 mb-12">
        <QuoteGridItem
          v-for="q in feed.quotes?.value"
          :key="(q as any).id"
          :quote="q"
        />
      </div>

      <div v-if="feed.hasMore?.value" class="text-center">
        <UButton
          @click="feed.loadMore"
          :loading="feed.loadingMore?.value"
          :disabled="feed.loadingMore?.value"
          size="sm"
          btn="solid-black"
          class="px-8 py-6 w-full rounded-3 hover:scale-101 active:scale-99 transition-transform duration-300 ease-in-out"
        >
          {{ feed.loadingMore?.value ? 'Loading...' : 'Load More Quotes' }}
        </UButton>
      </div>
    </div>

    <!-- Recent Quotes Section -->
    <div class="px-6 py-4">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-bold text-gray-900 dark:text-white">
          Latest Quotes
        </h2>
        <UButton
          btn="ghost-gray"
          size="sm"
          @click="navigateTo('/search')"
        >
          <UIcon name="i-ph-magnifying-glass-bold" class="w-4 h-4 mr-1" />
          <span class="text-sm">Search</span>
        </UButton>
      </div>

      <!-- Quote List -->
      <div v-if="feed.quotes?.value && feed.quotes.value.length > 0" class="space-y-4">
        <QuoteListItem
          v-for="q in feed.quotes.value.slice(0, 5)"
          :key="(q as any).id"
          :quote="q"
          class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700"
        />
      </div>

      <!-- Load More Button -->
      <div v-if="feed.hasMore?.value" class="mt-6">
        <UButton
          @click="feed.loadMore"
          :loading="feed.loadingMore?.value"
          :disabled="feed.loadingMore?.value"
          size="md"
          btn="outline-gray"
          class="w-full py-4 rounded-xl hover:scale-101 active:scale-99 transition-transform duration-300 ease-in-out"
        >
          {{ feed.loadingMore?.value ? 'Loading...' : 'Load More Quotes' }}
        </UButton>
      </div>
    </div>

    <!-- Add Quote Drawer (Mobile) -->
    <UDrawer v-model="showAddQuoteDrawer" side="bottom">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-900 dark:text-white">
            Add New Quote
          </h2>
          <UButton
            icon
            btn="ghost-gray"
            label="i-ph-x-bold"
            size="sm"
            @click="showAddQuoteDrawer = false"
          />
        </div>

        <form @submit.prevent="submitQuote" class="space-y-6">
          <div>
            <UInput
              type="textarea"
              autofocus
              v-model="quoteForm.content"
              class="text-size-6 font-600 font-subtitle border-dashed
                focus-visible:border-gray-700 ring-transparent light:focus-visible:ring-transparent dark:focus-visible:ring-transparent dark:focus-visible:border-gray-300"
              placeholder="Enter the quote content..."
              :rows="4"
              :disabled="submittingQuote"
              required
            />
            <!-- Character Counter -->
            <div class="mt-2 text-right">
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ quoteForm.content.length }} characters
              </span>
            </div>
          </div>

          <div class="grid grid-cols-1 gap-4">
            <!-- Language Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Language
              </label>
              <div>
                <USelect
                  v-model="quoteForm.language"
                  :items="languageOptions"
                  placeholder="Select language"
                  item-key="label"
                  value-key="label"
                  :disabled="submittingQuote"
                />
              </div>
            </div>

            <!-- Author Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Author (Optional)
              </label>
              <div>
                <UInput
                  v-model="authorSearchQuery"
                  placeholder="Search for an author..."
                  :disabled="submittingQuote"
                  @input="searchAuthors"
                />
                <div v-if="authorSearchResults.length > 0" class="mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div
                    v-for="author in authorSearchResults"
                    :key="author.id"
                    class="p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                    @click="selectAuthor(author)"
                  >
                    <div class="flex items-center space-x-3">
                      <img
                        v-if="author.image_url"
                        :src="author.image_url"
                        :alt="author.name"
                        class="w-8 h-8 rounded-full object-cover"
                      />
                      <div
                        v-else
                        class="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-xs font-bold text-gray-600 dark:text-gray-300"
                      >
                        {{ getAuthorInitials(author.name) }}
                      </div>
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ author.name }}</p>
                        <p v-if="author.job" class="text-xs text-gray-500 dark:text-gray-400">{{ author.job }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="quoteForm.selectedAuthor" class="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <img
                        v-if="quoteForm.selectedAuthor.image_url"
                        :src="quoteForm.selectedAuthor.image_url"
                        :alt="quoteForm.selectedAuthor.name"
                        class="w-8 h-8 rounded-full object-cover"
                      />
                      <div
                        v-else
                        class="w-8 h-8 rounded-full bg-blue-300 dark:bg-blue-600 flex items-center justify-center text-xs font-bold text-blue-800 dark:text-blue-200"
                      >
                        {{ getAuthorInitials(quoteForm.selectedAuthor.name) }}
                      </div>
                      <div>
                        <p class="text-sm font-medium text-blue-900 dark:text-blue-100">{{ quoteForm.selectedAuthor.name }}</p>
                        <p v-if="quoteForm.selectedAuthor.job" class="text-xs text-blue-700 dark:text-blue-300">{{ quoteForm.selectedAuthor.job }}</p>
                      </div>
                    </div>
                    <UButton
                      icon
                      btn="ghost-gray"
                      label="i-ph-x-bold"
                      size="xs"
                      @click="clearAuthor"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex space-x-3 pt-4">
            <UButton
              btn="light:soft dark:soft-white"
              @click="showAddQuoteDrawer = false"
              :disabled="submittingQuote"
              class="flex-1"
            >
              Cancel
            </UButton>
            <UButton
              btn="soft-blue"
              :loading="submittingQuote"
              type="submit"
              :disabled="!quoteForm.content.trim()"
              class="flex-1"
            >
              Submit Quote
            </UButton>
          </div>
        </form>
      </div>
    </UDrawer>
  </div>
</template>

<script lang="ts" setup>
import type { Author } from '~/types'

const { isMobile } = useMobileDetection()
const { currentLayout } = useLayoutSwitching()
const { user } = useUserSession()

// Set layout based on device
definePageMeta({
  layout: false // We'll handle layout switching dynamically
})

useHead({
  title: 'Verbatims • Universal Quotes',
  meta: [
    {
      name: 'description',
      content: 'Discover inspiring quotes from authors, films, books, and more. A comprehensive, user-generated quotes database with moderation capabilities.',
    }
  ]
})

const { isLanguageReady } = useLanguageReady()
const feed = useQuoteSearchFeed()

const { data: statsData } = await useFetch('/api/stats')
const { data: onboardingData } = await useFetch('/api/onboarding/status')

const stats = computed(() => statsData.value?.data || { quotes: 0, authors: 0, references: 0, users: 0 })
const onboardingStatus = computed(() => onboardingData.value?.data)
const needsOnboarding = computed(() => onboardingStatus.value?.needsOnboarding || false)

// Add Quote Drawer State
const showAddQuoteDrawer = ref(false)
const submittingQuote = ref(false)
const authorSearchQuery = ref('')
const authorSearchResults = ref<Author[]>([])

// Quote Form
const quoteForm = ref({
  content: '',
  language: { label: 'English', value: 'en' },
  selectedAuthor: null as Author | null
})

// Language options
const languageOptions = [
  { label: 'English', value: 'en' },
  { label: 'French', value: 'fr' },
  { label: 'Spanish', value: 'es' },
  { label: 'German', value: 'de' },
  { label: 'Italian', value: 'it' },
  { label: 'Portuguese', value: 'pt' },
  { label: 'Japanese', value: 'ja' },
  { label: 'Korean', value: 'ko' },
  { label: 'Chinese', value: 'zh' },
  { label: 'Russian', value: 'ru' },
  { label: 'Arabic', value: 'ar' },
  { label: 'Hindi', value: 'hi' }
]

onMounted(() => {
  setPageLayout(currentLayout.value)
  feed.init()
})

// Open add quote drawer
const openAddQuoteDrawer = () => {
  showAddQuoteDrawer.value = true
}

// Handle add author action
const handleAddAuthor = () => {
  // For now, navigate to authors page or show add quote dialog
  navigateTo('/authors')
}

// Handle add reference action
const handleAddReference = () => {
  // For now, navigate to references page
  navigateTo('/references')
}

// Author search functionality
const searchAuthors = async () => {
  if (authorSearchQuery.value.length < 2) {
    authorSearchResults.value = []
    return
  }

  try {
    const response = await $fetch('/api/authors/search', {
      query: {
        q: authorSearchQuery.value,
        limit: 10
      }
    })

    if (response.success) {
      authorSearchResults.value = response.data || []
    }
  } catch (error) {
    console.error('Failed to search authors:', error)
    authorSearchResults.value = []
  }
}

// Select author from search results
const selectAuthor = (author: Author) => {
  quoteForm.value.selectedAuthor = author
  authorSearchQuery.value = ''
  authorSearchResults.value = []
}

// Clear selected author
const clearAuthor = () => {
  quoteForm.value.selectedAuthor = null
}

// Get author initials for avatar fallback
const getAuthorInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

// Submit quote
const submitQuote = async () => {
  if (!quoteForm.value.content.trim()) return

  try {
    submittingQuote.value = true

    const payload = {
      content: quoteForm.value.content.trim(),
      language: quoteForm.value.language.value,
      author_id: quoteForm.value.selectedAuthor?.id || null,
      user_id: user.value?.id,
      status: 'draft' as const
    }

    await $fetch('/api/quotes', {
      method: 'POST',
      body: payload
    })

    // Reset form
    quoteForm.value.content = ''
    quoteForm.value.selectedAuthor = null
    authorSearchQuery.value = ''
    authorSearchResults.value = []

    // Close drawer
    showAddQuoteDrawer.value = false

    // Show success message
    useToast().toast({
      toast: 'success',
      title: 'Quote Added',
      description: 'Your quote has been submitted successfully!'
    })

    // Refresh feed
    feed.refresh()
  } catch (error) {
    console.error('Error submitting quote:', error)
    useToast().toast({
      toast: 'error',
      title: 'Error',
      description: 'Failed to add quote. Please try again.'
    })
  } finally {
    submittingQuote.value = false
  }
}

// Watch for layout changes and update accordingly
watch(currentLayout, (newLayout) => {
  setPageLayout(newLayout)
  console.log(`currentLayout: `, currentLayout.value)
})
</script>